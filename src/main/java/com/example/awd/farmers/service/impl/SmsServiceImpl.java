package com.example.awd.farmers.service.impl;

import com.example.awd.farmers.exception.ValidationException;
import com.example.awd.farmers.security.Constants;
import com.example.awd.farmers.service.MessageTemplateService;
import com.example.awd.farmers.service.NotificationTemplateService;
import com.example.awd.farmers.service.NotificationTemplateService.NotificationType;
import com.example.awd.farmers.service.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import javax.annotation.PostConstruct; // Import this annotation
import java.util.Map;

@Slf4j
@Service
public class SmsServiceImpl implements SmsService {


    @Value("${sms.gateway.base-url}")
    private String baseUrl;

    @Value("${sms.gateway.login-id}")
    private String loginId;

    @Value("${sms.gateway.password}")
    private String password;

    @Value("${sms.gateway.sender-id}")
    private String senderId;

    @Value("${sms.gateway.route-id}")
    private String routeId;

    @Value("${sms.gateway.template-id}")
    private String templateId;

    // Remove 'final' keyword as it's no longer initialized in the constructor
    private WebClient webClient;

    // Inject WebClient.Builder into the constructor
    private final WebClient.Builder webClientBuilder; // Keep this final

    public SmsServiceImpl(
            WebClient.Builder webClientBuilder
           ) {
        this.webClientBuilder = webClientBuilder; // Store the builder
    }

    @PostConstruct
    public void init() {
        System.out.println("SmsService: Initializing WebClient with baseUrl: " + baseUrl); // Diagnostic print
        this.webClient = webClientBuilder.baseUrl(baseUrl).build();
    }

    /**
     * Sends a single SMS message.
     */
    @Override
    public Mono<String> sendSingleSms(String mobile, String message) {
        System.out.println("Sending SMS to: " + mobile + " with message: " + message);

        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/API/pushsms.aspx") // Path from the base URL
                        .queryParam("loginID", loginId)
                        .queryParam("password", password)
                        .queryParam("mobile", mobile)
                        .queryParam("text", message)
                        .queryParam("senderid", senderId)
                        .queryParam("route_id", routeId)
                        .queryParam("Unicode", "0")
                        .build())
                .retrieve()
                .bodyToMono(String.class)
                .doOnSuccess(response -> System.out.println("SMS Gateway Response: " + response))
                .doOnError(error -> System.err.println("Error sending SMS: " + error.getMessage()));
    }

    /**
     * Sends multiple SMS messages (comma-separated mobile numbers).
     */
    @Override
    public Mono<String> sendMultipleSms(String mobiles, String message) {
        System.out.println("Sending multiple SMS to: " + mobiles + " with message: " + message);

        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/API/pushsms.aspx")
                        .queryParam("loginID", loginId)
                        .queryParam("password", password)
                        .queryParam("mobile", mobiles)
                        .queryParam("text", message)
                        .queryParam("senderid", senderId)
                        .queryParam("route_id", routeId)
                        .queryParam("Unicode", "0")
                        .build())
                .retrieve()
                .bodyToMono(String.class)
                .doOnSuccess(response -> System.out.println("Multiple SMS Gateway Response: " + response))
                .doOnError(error -> System.err.println("Error sending multiple SMS: " + error.getMessage()));
    }

    /**
     * Sends a Unicode SMS message.
     */
    @Override
    public Mono<String> sendUnicodeSms(String mobile, String message) {
        System.out.println("Sending Unicode SMS to: " + mobile + " with message: " + message);

        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/API/pushsms.aspx")
                        .queryParam("loginID", loginId)
                        .queryParam("password", password)
                        .queryParam("mobile", mobile)
                        .queryParam("text", message)
                        .queryParam("senderid", senderId)
                        .queryParam("route_id", routeId)
                        .queryParam("Unicode", "1")
                        .build())
                .retrieve()
                .bodyToMono(String.class)
                .doOnSuccess(response -> System.out.println("Unicode SMS Gateway Response: " + response))
                .doOnError(error -> System.err.println("Error sending Unicode SMS: " + error.getMessage()));
    }

    /**
     * Sends a Scheduled SMS message.
     */
    @Override
    public Mono<String> sendScheduledSms(String mobile, String message, String scheduleDateTime) {
        System.out.println("Scheduling SMS to: " + mobile + " with message: " + message + " at " + scheduleDateTime);

        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/API/pushsms.aspx")
                        .queryParam("loginID", loginId)
                        .queryParam("password", password)
                        .queryParam("mobile", mobile)
                        .queryParam("text", message)
                        .queryParam("senderid", senderId)
                        .queryParam("route_id", routeId)
                        .queryParam("Unicode", "0")
                        .queryParam("sch", scheduleDateTime)
                        .build())
                .retrieve()
                .bodyToMono(String.class)
                .doOnSuccess(response -> System.out.println("Scheduled SMS Gateway Response: " + response))
                .doOnError(error -> System.err.println("Error scheduling SMS: " + error.getMessage()));
    }


}
